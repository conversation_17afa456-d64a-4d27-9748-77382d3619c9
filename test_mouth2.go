package main

import (
	"image"
	"image/color"
	"math"
	"os"
	"path/filepath"

	"github.com/disintegration/imaging"
	"gocv.io/x/gocv"
)

func main() {
	// 1. 加载原始图像
	inputPath := "input.jpg"
	outputDir := "output"
	os.MkdirAll(outputDir, 0755)

	// 2. 检测人脸关键点
	faceRect, mouthPoints := detectFaceAndMouth(inputPath)
	if len(mouthPoints) < 2 {
		panic("未检测到嘴巴区域")
	}

	// 3. 加载原始图像
	img, err := imaging.Open(inputPath)
	if err != nil {
		panic(err)
	}

	// 4. 创建不同嘴巴形状
	mouthShapes := []struct {
		name  string
		shape func(image.Rectangle) []image.Point
	}{
		{"closed", createClosedMouth},
		{"slightly_open", createSlightlyOpenMouth},
		{"half_open", createHalfOpenMouth},
		{"fully_open", createFullyOpenMouth},
		{"wide_open", createWideOpenMouth},
	}

	// 5. 生成不同嘴巴状态的图像
	for i, shape := range mouthShapes {
		// 创建新图像副本
		newImg := imaging.Clone(img)

		// 创建当前嘴巴形状
		newMouth := shape.shape(mouthPoints[0])

		// 应用嘴巴变形
		result := applyMouthShape(newImg, mouthPoints, newMouth, faceRect)

		// 保存结果
		outputPath := filepath.Join(outputDir, "output_"+shape.name+".jpg")
		if err := imaging.Save(result, outputPath); err != nil {
			panic(err)
		}

		// 创建过渡帧（增加动画流畅度）
		if i > 0 {
			for j := 1; j <= 2; j++ {
				prevShape := mouthShapes[i-1].shape(mouthPoints[0])
				interpolated := interpolateMouth(prevShape, newMouth, float64(j)/3.0)
				result = applyMouthShape(newImg, mouthPoints, interpolated, faceRect)
				outputPath := filepath.Join(outputDir, "output_"+shape.name+"_transition_"+string(rune('a'+j))+".jpg")
				imaging.Save(result, outputPath)
			}
		}
	}
}

// 检测人脸和嘴巴区域
func detectFaceAndMouth(imagePath string) (image.Rectangle, [][][]image.Point) {
	// 初始化OpenCV
	net := gocv.ReadNet("opencv_face_detector.pbtxt", "opencv_face_detector_uint8.pb")
	if net.Empty() {
		panic("无法加载人脸检测模型")
	}
	defer net.Close()

	// 加载面部关键点检测器
	predictor := gocv.NewCascadeClassifier()
	if !predictor.Load("shape_predictor_68_face_landmarks.dat") {
		panic("无法加载面部关键点模型")
	}
	defer predictor.Close()

	// 读取图像
	img := gocv.IMRead(imagePath, gocv.IMReadColor)
	if img.Empty() {
		panic("无法读取图像")
	}
	defer img.Close()

	// 人脸检测
	blob := gocv.BlobFromImage(img, 1.0, image.Pt(300, 300), gocv.NewScalar(104, 177, 123, 0), false, false)
	defer blob.Close()
	net.SetInput(blob, "")
	detections := gocv.Mat{}
	defer detections.Close()
	net.Forward(&detections, "")

	// 处理检测结果
	detectionsMat := detections.Reshape(1, int(detections.Total()/7))
	defer detectionsMat.Close()

	var faceRect image.Rectangle
	var mouthPoints [][][]image.Point

	for i := 0; i < detectionsMat.Rows(); i++ {
		confidence := detectionsMat.GetFloatAt(i, 2)
		if confidence < 0.5 {
			continue
		}

		// 计算人脸坐标
		left := int(float32(img.Cols()) * detectionsMat.GetFloatAt(i, 3))
		top := int(float32(img.Rows()) * detectionsMat.GetFloatAt(i, 4))
		right := int(float32(img.Cols()) * detectionsMat.GetFloatAt(i, 5))
		bottom := int(float32(img.Rows()) * detectionsMat.GetFloatAt(i, 6))

		faceRect = image.Rect(left, top, right, bottom)

		// 检测面部关键点
		faceROI := img.Region(faceRect)
		keypoints := predictor.DetectMultiScaleWithParams(faceROI, 1.1, 3, 0, image.Pt(0, 0), image.Pt(0, 0))

		if len(keypoints) > 0 {
			// 嘴部关键点索引 (48-67)
			mouth := make([][]image.Point, 1)
			mouth[0] = make([]image.Point, 20)
			for i := 48; i <= 67; i++ {
				if i-48 < len(keypoints[0].Points) {
					pt := keypoints[0].Points[i]
					mouth[0][i-48] = image.Pt(
						int(pt.X)+faceRect.Min.X,
						int(pt.Y)+faceRect.Min.Y,
					)
				}
			}
			mouthPoints = append(mouthPoints, mouth)
		}
	}

	return faceRect, mouthPoints
}

// 创建不同嘴巴形状的函数
func createClosedMouth(bounds image.Rectangle) []image.Point {
	width := bounds.Dx()
	height := bounds.Dy()
	center := bounds.Min.Add(bounds.Max).Div(2)

	return []image.Point{
		{center.X - width/3, center.Y},
		{center.X - width/6, center.Y + height/10},
		{center.X, center.Y + height/8},
		{center.X + width/6, center.Y + height/10},
		{center.X + width/3, center.Y},
	}
}

func createSlightlyOpenMouth(bounds image.Rectangle) []image.Point {
	width := bounds.Dx()
	height := bounds.Dy()
	center := bounds.Min.Add(bounds.Max).Div(2)

	return []image.Point{
		{center.X - width/3, center.Y - height/20},
		{center.X - width/6, center.Y + height/8},
		{center.X, center.Y + height/6},
		{center.X + width/6, center.Y + height/8},
		{center.X + width/3, center.Y - height/20},
	}
}

func createHalfOpenMouth(bounds image.Rectangle) []image.Point {
	width := bounds.Dx()
	height := bounds.Dy()
	center := bounds.Min.Add(bounds.Max).Div(2)

	return []image.Point{
		{center.X - width/3, center.Y - height/10},
		{center.X - width/6, center.Y + height/6},
		{center.X, center.Y + height/4},
		{center.X + width/6, center.Y + height/6},
		{center.X + width/3, center.Y - height/10},
	}
}

func createFullyOpenMouth(bounds image.Rectangle) []image.Point {
	width := bounds.Dx()
	height := bounds.Dy()
	center := bounds.Min.Add(bounds.Max).Div(2)

	return []image.Point{
		{center.X - width/3, center.Y - height/6},
		{center.X - width/6, center.Y + height/5},
		{center.X, center.Y + height/3},
		{center.X + width/6, center.Y + height/5},
		{center.X + width/3, center.Y - height/6},
	}
}

func createWideOpenMouth(bounds image.Rectangle) []image.Point {
	width := bounds.Dx()
	height := bounds.Dy()
	center := bounds.Min.Add(bounds.Max).Div(2)

	return []image.Point{
		{center.X - width/2, center.Y - height/5},
		{center.X - width/4, center.Y + height/4},
		{center.X, center.Y + height/2},
		{center.X + width/4, center.Y + height/4},
		{center.X + width/2, center.Y - height/5},
	}
}

// 应用嘴巴形状到图像
func applyMouthShape(img image.Image, originalMouth [][][]image.Point, newShape []image.Point, faceRect image.Rectangle) *image.NRGBA {
	// 创建新图像
	result := imaging.Clone(img)

	// 获取原始嘴巴颜色
	mouthColor := calculateMouthColor(img, originalMouth[0][0])

	// 绘制新嘴巴
	result = drawMouth(result, newShape, mouthColor)

	// 添加阴影效果增强真实感
	result = addMouthShadow(result, newShape, faceRect)

	return result
}

// 计算嘴巴区域的平均颜色
func calculateMouthColor(img image.Image, points []image.Point) color.Color {
	minX, minY := points[0].X, points[0].Y
	maxX, maxY := minX, minY

	for _, pt := range points {
		if pt.X < minX {
			minX = pt.X
		}
		if pt.Y < minY {
			minY = pt.Y
		}
		if pt.X > maxX {
			maxX = pt.X
		}
		if pt.Y > maxY {
			maxY = pt.Y
		}
	}

	rect := image.Rect(minX, minY, maxX, maxY)
	avgColor := imaging.Crop(img, rect)
	return imaging.AverageColor(avgColor)
}

// 绘制嘴巴形状
func drawMouth(img *image.NRGBA, points []image.Point, mouthColor color.Color) *image.NRGBA {
	// 创建路径
	poly := make([]imaging.Point, len(points))
	for i, pt := range points {
		poly[i] = imaging.Point{X: pt.X, Y: pt.Y}
	}

	// 绘制嘴巴（带抗锯齿）
	img = imaging.FillPolygon(img, poly, mouthColor, imaging.AntiAlias)

	// 添加嘴巴内部细节
	center := calculateCenter(points)
	img = imaging.DrawCircle(img, center.X, center.Y, points[0].X/20, color.Black, 1)

	return img
}

// 添加嘴巴阴影增强真实感
func addMouthShadow(img *image.NRGBA, points []image.Point, faceRect image.Rectangle) *image.NRGBA {
	center := calculateCenter(points)
	shadowColor := color.RGBA{0, 0, 0, 30} // 半透明黑色

	// 在嘴巴下方添加阴影
	for i := 0; i < 5; i++ {
		radius := faceRect.Dx()/4 + i*2
		img = imaging.Blur(img, 0.5+float64(i)*0.2)
		img = imaging.DrawCircle(img, center.X, center.Y+5+i, radius, shadowColor, 1)
	}

	return img
}

// 计算点集的中心
func calculateCenter(points []image.Point) image.Point {
	sumX, sumY := 0, 0
	for _, pt := range points {
		sumX += pt.X
		sumY += pt.Y
	}
	return image.Point{X: sumX / len(points), Y: sumY / len(points)}
}

// 插值生成中间嘴巴形状
func interpolateMouth(shape1, shape2 []image.Point, ratio float64) []image.Point {
	result := make([]image.Point, len(shape1))
	for i := range shape1 {
		dx := float64(shape2[i].X-shape1[i].X) * ratio
		dy := float64(shape2[i].Y-shape1[i].Y) * ratio
		result[i] = image.Point{
			X: shape1[i].X + int(math.Round(dx)),
			Y: shape1[i].Y + int(math.Round(dy)),
		}
	}
	return result
}
