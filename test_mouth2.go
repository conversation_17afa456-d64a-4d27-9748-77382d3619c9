package main

import (
	"image"
	"image/color"
	"math"
	"os"
	"path/filepath"

	"github.com/disintegration/imaging"
	"gocv.io/x/gocv"
)

func main() {
	// 1. 加载原始图像
	inputPath := "input.jpg"
	outputDir := "output"
	os.MkdirAll(outputDir, 0755)

	// 2. 检测人脸关键点
	faceRect, mouthPoints := detectFaceAndMouth(inputPath)
	if len(mouthPoints) < 2 {
		panic("未检测到嘴巴区域")
	}

	// 3. 加载原始图像
	img, err := imaging.Open(inputPath)
	if err != nil {
		panic(err)
	}

	// 4. 创建不同嘴巴形状
	mouthShapes := []struct {
		name  string
		shape func(image.Rectangle) []image.Point
	}{
		{"closed", createClosedMouth},
		{"slightly_open", createSlightlyOpenMouth},
		{"half_open", createHalfOpenMouth},
		{"fully_open", createFullyOpenMouth},
		{"wide_open", createWideOpenMouth},
	}

	// 5. 生成不同嘴巴状态的图像
	for i, shape := range mouthShapes {
		// 创建新图像副本
		newImg := imaging.Clone(img)

		// 创建当前嘴巴形状
		newMouth := shape.shape(mouthPoints[0])

		// 应用嘴巴变形
		result := applyMouthShape(newImg, mouthPoints, newMouth, faceRect)

		// 保存结果
		outputPath := filepath.Join(outputDir, "output_"+shape.name+".jpg")
		if err := imaging.Save(result, outputPath); err != nil {
			panic(err)
		}

		// 创建过渡帧（增加动画流畅度）
		if i > 0 {
			for j := 1; j <= 2; j++ {
				prevShape := mouthShapes[i-1].shape(mouthPoints[0])
				interpolated := interpolateMouth(prevShape, newMouth, float64(j)/3.0)
				result = applyMouthShape(newImg, mouthPoints, interpolated, faceRect)
				outputPath := filepath.Join(outputDir, "output_"+shape.name+"_transition_"+string(rune('a'+j))+".jpg")
				imaging.Save(result, outputPath)
			}
		}
	}
}

// 检测人脸和嘴巴区域
func detectFaceAndMouth(imagePath string) (image.Rectangle, [][][]image.Point) {
	// 使用Haar级联分类器进行人脸检测
	classifier := gocv.NewCascadeClassifier()
	defer classifier.Close()

	if !classifier.Load("haarcascade_frontalface_alt.xml") {
		// 如果没有模型文件，返回默认值
		bounds := image.Rect(100, 100, 400, 400)
		mouthPoints := [][][]image.Point{
			{{
				{bounds.Min.X + 50, bounds.Min.Y + 200},
				{bounds.Min.X + 100, bounds.Min.Y + 220},
				{bounds.Min.X + 150, bounds.Min.Y + 230},
				{bounds.Min.X + 200, bounds.Min.Y + 220},
				{bounds.Min.X + 250, bounds.Min.Y + 200},
			}},
		}
		return bounds, mouthPoints
	}

	// 读取图像
	img := gocv.IMRead(imagePath, gocv.IMReadColor)
	if img.Empty() {
		// 如果无法读取图像，返回默认值
		bounds := image.Rect(100, 100, 400, 400)
		mouthPoints := [][][]image.Point{
			{{
				{bounds.Min.X + 50, bounds.Min.Y + 200},
				{bounds.Min.X + 100, bounds.Min.Y + 220},
				{bounds.Min.X + 150, bounds.Min.Y + 230},
				{bounds.Min.X + 200, bounds.Min.Y + 220},
				{bounds.Min.X + 250, bounds.Min.Y + 200},
			}},
		}
		return bounds, mouthPoints
	}
	defer img.Close()

	// 转换为灰度图像
	gray := gocv.NewMat()
	defer gray.Close()
	gocv.CvtColor(img, &gray, gocv.ColorBGRToGray)

	// 检测人脸
	rects := classifier.DetectMultiScale(gray)

	var faceRect image.Rectangle
	var mouthPoints [][][]image.Point

	if len(rects) > 0 {
		// 使用第一个检测到的人脸
		rect := rects[0]
		faceRect = image.Rect(rect.Min.X, rect.Min.Y, rect.Max.X, rect.Max.Y)

		// 估算嘴巴位置（在人脸下半部分）
		mouthY := faceRect.Min.Y + int(float64(faceRect.Dy())*0.7)
		mouthWidth := int(float64(faceRect.Dx()) * 0.4)
		mouthCenterX := faceRect.Min.X + faceRect.Dx()/2

		mouth := [][]image.Point{{
			{mouthCenterX - mouthWidth/2, mouthY},
			{mouthCenterX - mouthWidth/4, mouthY + 10},
			{mouthCenterX, mouthY + 15},
			{mouthCenterX + mouthWidth/4, mouthY + 10},
			{mouthCenterX + mouthWidth/2, mouthY},
		}}
		mouthPoints = append(mouthPoints, mouth)
	} else {
		// 如果没有检测到人脸，返回默认值
		faceRect = image.Rect(100, 100, 400, 400)
		mouthPoints = [][][]image.Point{
			{{
				{faceRect.Min.X + 50, faceRect.Min.Y + 200},
				{faceRect.Min.X + 100, faceRect.Min.Y + 220},
				{faceRect.Min.X + 150, faceRect.Min.Y + 230},
				{faceRect.Min.X + 200, faceRect.Min.Y + 220},
				{faceRect.Min.X + 250, faceRect.Min.Y + 200},
			}},
		}
	}

	return faceRect, mouthPoints
}

// 创建不同嘴巴形状的函数
func createClosedMouth(bounds image.Rectangle) []image.Point {
	width := bounds.Dx()
	height := bounds.Dy()
	center := bounds.Min.Add(bounds.Max).Div(2)

	return []image.Point{
		{center.X - width/3, center.Y},
		{center.X - width/6, center.Y + height/10},
		{center.X, center.Y + height/8},
		{center.X + width/6, center.Y + height/10},
		{center.X + width/3, center.Y},
	}
}

func createSlightlyOpenMouth(bounds image.Rectangle) []image.Point {
	width := bounds.Dx()
	height := bounds.Dy()
	center := bounds.Min.Add(bounds.Max).Div(2)

	return []image.Point{
		{center.X - width/3, center.Y - height/20},
		{center.X - width/6, center.Y + height/8},
		{center.X, center.Y + height/6},
		{center.X + width/6, center.Y + height/8},
		{center.X + width/3, center.Y - height/20},
	}
}

func createHalfOpenMouth(bounds image.Rectangle) []image.Point {
	width := bounds.Dx()
	height := bounds.Dy()
	center := bounds.Min.Add(bounds.Max).Div(2)

	return []image.Point{
		{center.X - width/3, center.Y - height/10},
		{center.X - width/6, center.Y + height/6},
		{center.X, center.Y + height/4},
		{center.X + width/6, center.Y + height/6},
		{center.X + width/3, center.Y - height/10},
	}
}

func createFullyOpenMouth(bounds image.Rectangle) []image.Point {
	width := bounds.Dx()
	height := bounds.Dy()
	center := bounds.Min.Add(bounds.Max).Div(2)

	return []image.Point{
		{center.X - width/3, center.Y - height/6},
		{center.X - width/6, center.Y + height/5},
		{center.X, center.Y + height/3},
		{center.X + width/6, center.Y + height/5},
		{center.X + width/3, center.Y - height/6},
	}
}

func createWideOpenMouth(bounds image.Rectangle) []image.Point {
	width := bounds.Dx()
	height := bounds.Dy()
	center := bounds.Min.Add(bounds.Max).Div(2)

	return []image.Point{
		{center.X - width/2, center.Y - height/5},
		{center.X - width/4, center.Y + height/4},
		{center.X, center.Y + height/2},
		{center.X + width/4, center.Y + height/4},
		{center.X + width/2, center.Y - height/5},
	}
}

// 应用嘴巴形状到图像
func applyMouthShape(img image.Image, originalMouth [][][]image.Point, newShape []image.Point, faceRect image.Rectangle) *image.NRGBA {
	// 创建新图像
	result := imaging.Clone(img)

	// 获取原始嘴巴颜色
	mouthColor := calculateMouthColor(img, originalMouth[0][0])

	// 绘制新嘴巴
	result = drawMouth(result, newShape, mouthColor)

	// 添加阴影效果增强真实感
	result = addMouthShadow(result, newShape, faceRect)

	return result
}

// 计算嘴巴区域的平均颜色
func calculateMouthColor(img image.Image, points []image.Point) color.Color {
	minX, minY := points[0].X, points[0].Y
	maxX, maxY := minX, minY

	for _, pt := range points {
		if pt.X < minX {
			minX = pt.X
		}
		if pt.Y < minY {
			minY = pt.Y
		}
		if pt.X > maxX {
			maxX = pt.X
		}
		if pt.Y > maxY {
			maxY = pt.Y
		}
	}

	// 简单返回一个肉色
	return color.RGBA{R: 205, G: 133, B: 63, A: 255}
}

// 绘制嘴巴形状
func drawMouth(img *image.NRGBA, points []image.Point, mouthColor color.Color) *image.NRGBA {
	// 简单的填充椭圆形状来模拟嘴巴
	center := calculateCenter(points)

	// 计算嘴巴的宽度和高度
	minX, maxX := points[0].X, points[0].X
	minY, maxY := points[0].Y, points[0].Y
	for _, pt := range points {
		if pt.X < minX {
			minX = pt.X
		}
		if pt.X > maxX {
			maxX = pt.X
		}
		if pt.Y < minY {
			minY = pt.Y
		}
		if pt.Y > maxY {
			maxY = pt.Y
		}
	}

	width := maxX - minX
	height := maxY - minY

	// 绘制椭圆形嘴巴
	for y := minY; y <= maxY; y++ {
		for x := minX; x <= maxX; x++ {
			// 椭圆方程
			dx := float64(x - center.X)
			dy := float64(y - center.Y)
			if (dx*dx)/float64(width*width/4)+(dy*dy)/float64(height*height/4) <= 1 {
				if x >= 0 && y >= 0 && x < img.Bounds().Dx() && y < img.Bounds().Dy() {
					img.Set(x, y, mouthColor)
				}
			}
		}
	}

	return img
}

// 添加嘴巴阴影增强真实感
func addMouthShadow(img *image.NRGBA, points []image.Point, faceRect image.Rectangle) *image.NRGBA {
	// 简单的模糊处理来模拟阴影效果
	img = imaging.Blur(img, 0.5)
	return img
}

// 计算点集的中心
func calculateCenter(points []image.Point) image.Point {
	sumX, sumY := 0, 0
	for _, pt := range points {
		sumX += pt.X
		sumY += pt.Y
	}
	return image.Point{X: sumX / len(points), Y: sumY / len(points)}
}

// 插值生成中间嘴巴形状
func interpolateMouth(shape1, shape2 []image.Point, ratio float64) []image.Point {
	result := make([]image.Point, len(shape1))
	for i := range shape1 {
		dx := float64(shape2[i].X-shape1[i].X) * ratio
		dy := float64(shape2[i].Y-shape1[i].Y) * ratio
		result[i] = image.Point{
			X: shape1[i].X + int(math.Round(dx)),
			Y: shape1[i].Y + int(math.Round(dy)),
		}
	}
	return result
}
